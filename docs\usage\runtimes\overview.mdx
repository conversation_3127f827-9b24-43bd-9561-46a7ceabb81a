---
title: Overview
description: This section is for users that would like to use a runtime other than Dock<PERSON> for OpenHands.
---

A Runtime is an environment where the OpenHands agent can edit files and run
commands.

By default, OpenHands uses a [Docker-based runtime](/usage/runtimes/docker), running on your local computer.
This means you only have to pay for the LLM you're using, and your code is only ever sent to the LLM.

We also support other runtimes, which are typically managed by third-parties.

Additionally, we provide a [Local Runtime](/usage/runtimes/local) that runs directly on your machine without Docker,
which can be useful in controlled environments like CI pipelines.

## Available Runtimes

OpenHands supports several different runtime environments:

- [Docker Runtime](/usage/runtimes/docker) - The default runtime that uses Docker containers for isolation (recommended for most users).
- [OpenHands Remote Runtime](/usage/runtimes/remote) - Cloud-based runtime for parallel execution (beta).
- [Local Runtime](/usage/runtimes/local) - Direct execution on your local machine without Docker.
- And more third-party runtimes:
  - [Modal Runtime](/usage/runtimes/modal) - Runtime provided by our partners at Modal.
  - [Daytona Runtime](/usage/runtimes/daytona) - Runtime provided by Daytona.
