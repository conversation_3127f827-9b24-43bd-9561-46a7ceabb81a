---
title: About OpenHands
---

## Research Strategy

Achieving full replication of production-grade applications with LLMs is a complex endeavor. Our strategy involves:

- **Core Technical Research:** Focusing on foundational research to understand and improve the technical aspects of code generation and handling.
- **Task Planning:** Developing capabilities for bug detection, codebase management, and optimization.
- **Evaluation:** Establishing comprehensive evaluation metrics to better understand and improve our agents.

## Default Agent

Our default Agent is currently the [CodeActAgent](./agents), which is capable of generating code and handling files.

## Built With

OpenHands is built using a combination of powerful frameworks and libraries, providing a robust foundation for its
development. Here are the key technologies used in the project:

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

Please note that the selection of these technologies is in progress, and additional technologies may be added or
existing ones may be removed as the project evolves. We strive to adopt the most suitable and efficient tools to
enhance the capabilities of OpenHands.

## License

Distributed under MIT [License](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE).
