{"name": "openhands-frontend", "version": "0.44.0", "private": true, "type": "module", "engines": {"node": ">=20.0.0"}, "dependencies": {"@heroui/react": "^2.8.0-beta.7", "@microlink/react-json-view": "^1.26.2", "@monaco-editor/react": "^4.7.0-rc.0", "@react-router/node": "^7.6.2", "@react-router/serve": "^7.6.2", "@react-types/shared": "^3.29.1", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.80.7", "@vitejs/plugin-react": "^4.5.2", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.4.0", "axios": "^1.9.0", "clsx": "^2.1.1", "eslint-config-airbnb-typescript": "^18.0.0", "framer-motion": "^12.17.3", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "isbot": "^5.1.28", "jose": "^6.0.11", "lucide-react": "^0.514.0", "monaco-editor": "^0.52.2", "posthog-js": "^1.251.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-highlight": "^0.15.0", "react-hot-toast": "^2.5.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "remark-gfm": "^4.0.1", "sirv-cli": "^3.0.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "vite": "^6.3.5", "web-vitals": "^5.0.3", "ws": "^8.18.2"}, "scripts": {"dev": "npm run make-i18n && cross-env VITE_MOCK_API=false react-router dev", "dev:mock": "npm run make-i18n && cross-env VITE_MOCK_API=true VITE_MOCK_SAAS=false react-router dev", "dev:mock:saas": "npm run make-i18n && cross-env VITE_MOCK_API=true VITE_MOCK_SAAS=true react-router dev", "build": "npm run make-i18n && react-router build", "start": "npx sirv-cli build/ --single", "test": "vitest run", "test:e2e": "playwright test", "test:coverage": "npm run make-i18n && vitest run --coverage", "dev_wsl": "VITE_WATCH_USE_POLLING=true vite", "preview": "vite preview", "make-i18n": "node scripts/make-i18n-translations.cjs", "prelint": "npm run make-i18n", "lint": "npm run typecheck && eslint src --ext .ts,.tsx,.js && prettier --check src/**/*.{ts,tsx}", "lint:fix": "eslint src --ext .ts,.tsx,.js --fix && prettier --write src/**/*.{ts,tsx}", "prepare": "cd .. && husky frontend/.husky", "typecheck": "react-router typegen && tsc", "check-unlocalized-strings": "node scripts/check-unlocalized-strings.cjs", "check-translation-completeness": "node scripts/check-translation-completeness.cjs"}, "lint-staged": {"src/**/*.{ts,tsx,js}": ["eslint --fix", "prettier --write"]}, "devDependencies": {"@babel/parser": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.0", "@mswjs/socket.io-binding": "^0.1.1", "@playwright/test": "^1.53.0", "@react-router/dev": "^7.6.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.78.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.1", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-highlight": "^0.12.8", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^3.2.3", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "msw": "^2.6.6", "prettier": "^3.5.3", "stripe": "^18.2.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.2"}, "packageManager": "npm@10.5.0", "volta": {"node": "18.20.1"}, "msw": {"workerDirectory": ["public"]}}