# Workflow that marks issues and PRs with no activity for 30 days with "<PERSON><PERSON>" and closes them after 7 more days of no activity
name: 'Close stale issues'

# Runs every day at 01:30
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/stale@v9
        with:
          stale-issue-message: 'This issue is stale because it has been open for 30 days with no activity. Remove stale label or comment or this will be closed in 7 days.'
          stale-pr-message: 'This PR is stale because it has been open for 30 days with no activity. Remove stale label or comment or this will be closed in 7 days.'
          days-before-stale: 30
          exempt-issue-labels: 'tracked'
          close-issue-message: 'This issue was closed because it has been stalled for over 30 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for over 30 days with no activity.'
          days-before-close: 7
          operations-per-run: 150
