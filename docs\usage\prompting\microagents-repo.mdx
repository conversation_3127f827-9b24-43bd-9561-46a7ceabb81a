---
title: General Microagents
description: General guidelines for OpenHands to work more effectively with the repository.
---

## Usage

These microagents are always loaded as part of the context.

## Frontmatter Syntax

The frontmatter for this type of microagent is optional.

Frontmatter should be enclosed in triple dashes (---) and may include the following fields:

| Field     | Description                             | Required | Default        |
|-----------|-----------------------------------------|----------|----------------|
| `agent`   | The agent this microagent applies to    | No       | 'CodeActAgent' |

## Creating a Comprehensive Repository Agent

To create an effective repository agent, you can ask OpenHands to analyze your repository with a prompt like:

```
Please browse the repository, look at the documentation and relevant code, and understand the purpose of this repository.

Specifically, I want you to create a `.openhands/microagents/repo.md` file. This file should contain succinct information that summarizes:
1. The purpose of this repository
2. The general setup of this repo
3. A brief description of the structure of this repo

Read all the GitHub workflows under .github/ of the repository (if this folder exists) to understand the CI checks (e.g., linter, pre-commit), and include those in the repo.md file.
```

This approach helps OpenHands capture repository context efficiently, reducing the need for repeated searches during conversations and ensuring more accurate solutions.

## Example Content

A comprehensive repository agent file (`.openhands/microagents/repo.md`) should include:

```
# Repository Purpose
This project is a TODO application that allows users to track TODO items.

# Setup Instructions
To set it up, you can run `npm run build`.

# Repository Structure
- `/src`: Core application code
- `/tests`: Test suite
- `/docs`: Documentation
- `/.github`: CI/CD workflows

# CI/CD Workflows
- `lint.yml`: Runs ESLint on all JavaScript files
- `test.yml`: Runs the test suite on pull requests

# Development Guidelines
Always make sure the tests are passing before committing changes. You can run the tests by running `npm run test`.
```

[See more examples of general microagents here.](https://github.com/All-Hands-AI/OpenHands/tree/main/.openhands/microagents)
