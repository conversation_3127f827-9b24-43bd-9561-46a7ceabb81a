# ✅ Providing Feedback

When using OpenHands, you will encounter cases where things work well, and others where they don't. We encourage you to
provide feedback when you use OpenHands to help give feedback to the development team, and perhaps more importantly,
create an open corpus of coding agent training examples -- Share-OpenHands!

## 📝 How to Provide Feedback

Providing feedback is easy! When you are using OpenHands, you can press the thumbs-up or thumbs-down button at any point
during your interaction. You will be prompted to provide your email address
(e.g. so we can contact you if we want to ask any follow-up questions), and you can choose whether you want to provide feedback publicly or privately.

<iframe width="560" height="315" src="https://www.youtube.com/embed/5rFx-StMVV0?si=svo7xzp6LhGK_GXr" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

## 📜 Data Use and Privacy

### Data sharing settings

When you submit data, you can submit it either publicly or privately.

- **Public** data will be distributed under the MIT License, like OpenHands itself, and can be used by the community to
train and test models. Obviously, feedback that you can make public will be more valuable for the community as a whole,
so when you are not dealing with sensitive information, we would encourage you to choose this option!
- **Private** data will be made available to the OpenHands team for the purpose of improving OpenHands.
However, a link with a unique ID will still be created that you can share publicly with others.

### Who collects and stores the data?

The data is collected and stored by [All Hands AI](https://all-hands.dev), a company founded by OpenHands maintainers to support and improve OpenHands.

### How will public data be released?

The public data will be released when we hit fixed milestones, such as 1,000 public examples, 10,000 public examples, etc.
At this time, we will follow the following release process:

1. All people who contributed public feedback will receive an email describing the data release and being given an opportunity to opt out.
2. The person or people in charge of the data release will perform quality control of the data, removing low-quality feedback,
removing email submitter email addresses, and attempting to remove any sensitive information.
3. The data will be released publicly under the MIT license through commonly used sites such as GitHub or Hugging Face.

### What if I want my data deleted?

For data on the All Hands AI servers, we are happy to delete it at request:

**One Piece of Data:** If you want one piece of data deleted, we will shortly be adding a mechanism to delete pieces of
data using the link and password that is displayed on the interface when you submit data.

**All Data:** If you would like all pieces of your data deleted, or you do not have the ID and password that you
received when submitting the data, please contact `<EMAIL>` from the email address that you registered
when you originally submitted the data.
