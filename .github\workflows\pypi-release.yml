# Publishes the OpenHands PyPi package
name: Publish PyPi Package

# Triggered manually
on:
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual trigger'
        required: true
        default: ''

jobs:
  release:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - uses: useblacksmith/setup-python@v6
        with:
          python-version: 3.12
      - name: Install Poetry
        uses: snok/install-poetry@v1.4.1
        with:
          virtualenvs-in-project: true
          virtualenvs-path: ~/.virtualenvs
      - name: Install Poetry Dependencies
        run: poetry install --no-interaction --no-root
      - name: Build poetry project
        run: ./build.sh
      - name: publish
        run: poetry publish -u __token__ -p ${{ secrets.PYPI_TOKEN }}
