---
title: Global Microagents
description: Global microagents are [keyword-triggered microagents](./microagents-keyword) that apply to all OpenHands users. A list of the current global microagents can be found [in the OpenHands repository](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents).
---

## Contributing a Global Microagent

You can create global microagents and share with the community by opening a pull request to the official repository.

See the [CONTRIBUTING.md](https://github.com/All-Hands-AI/OpenHands/blob/main/CONTRIBUTING.md) for specific instructions on how to contribute to OpenHands.

### Global Microagents Best Practices

- **Clear Scope**: Keep the microagent focused on a specific domain or task.
- **Explicit Instructions**: Provide clear, unambiguous guidelines.
- **Useful Examples**: Include practical examples of common use cases.
- **Safety First**: Include necessary warnings and constraints.
- **Integration Awareness**: Consider how the microagent interacts with other components.

### Steps to Contribute a Global Microagent

#### 1. Plan the Global Microagent

Before creating a global microagent, consider:

- What specific problem or use case will it address?
- What unique capabilities or knowledge should it have?
- What trigger words make sense for activating it?
- What constraints or guidelines should it follow?

#### 2. Create File

Create a new Markdown file with a descriptive name in the appropriate directory:
[`microagents/`](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)

#### 3. Testing the Global Microagent

- Test the agent with various prompts.
- Verify trigger words activate the agent correctly.
- Ensure instructions are clear and comprehensive.
- Check for potential conflicts and overlaps with existing agents.

#### 4. Submission Process

Submit a pull request with:

- The new microagent file.
- Updated documentation if needed.
- Description of the agent's purpose and capabilities.
