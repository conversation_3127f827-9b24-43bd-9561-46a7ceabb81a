[build-system]
build-backend = "poetry.core.masonry.api"
requires = [
  "poetry-core",
]

[tool.poetry]
name = "openhands-ai"
version = "0.44.0"
description = "OpenHands: Code Less, Make More"
authors = [ "OpenHands" ]
license = "MIT"
readme = "README.md"
repository = "https://github.com/All-Hands-AI/OpenHands"
packages = [
  { include = "openhands/**/*" },
  { include = "pyproject.toml", to = "openhands" },
  { include = "poetry.lock", to = "openhands" },
]

[tool.poetry.dependencies]
python = "^3.12,<3.14"
litellm = "^1.60.0, !=1.64.4, !=1.67.*"            # avoid 1.64.4 (known bug) & 1.67.* (known bug #10272)
aiohttp = ">=3.9.0,!=3.11.13"                      # Pin to avoid yanked version 3.11.13
google-generativeai = "*"                          # To use litellm with Gemini Pro API
google-api-python-client = "^2.164.0"              # For Google Sheets API
google-auth-httplib2 = "*"                         # For Google Sheets authentication
google-auth-oauthlib = "*"                         # For Google Sheets OAuth
termcolor = "*"
docker = "*"
fastapi = "*"
toml = "*"
types-toml = "*"
uvicorn = "*"
numpy = "*"
json-repair = "*"
browsergym-core = "0.13.3"                         # integrate browsergym-core as the browsing interface
html2text = "*"
e2b = ">=1.0.5,<1.6.0"
pexpect = "*"
jinja2 = "^3.1.3"
python-multipart = "*"
tenacity = ">=8.5,<10.0"
zope-interface = "7.2"
pathspec = "^0.12.1"
pyjwt = "^2.9.0"
dirhash = "*"
tornado = "*"
python-dotenv = "*"
rapidfuzz = "^3.9.0"
whatthepatch = "^1.0.6"
protobuf = "^5.0.0,<6.0.0"                         # Updated to support newer opentelemetry
opentelemetry-api = "^1.33.1"
opentelemetry-exporter-otlp-proto-grpc = "^1.33.1"
modal = ">=0.66.26,<1.1.0"
runloop-api-client = "0.42.0"
libtmux = ">=0.37,<0.40"
pygithub = "^2.5.0"
joblib = "*"
openhands-aci = "0.3.0"
python-socketio = "^5.11.4"
sse-starlette = "^2.1.3"
psutil = "*"
python-json-logger = "^3.2.1"
prompt-toolkit = "^3.0.50"
poetry = "^2.1.2"
anyio = "4.9.0"
pythonnet = "*"
fastmcp = "^2.5.2"
python-frontmatter = "^1.1.0"
# TODO: Should these go into the runtime group?
ipywidgets = "^8.1.5"
qtconsole = "^5.6.1"
PyPDF2 = "*"
python-pptx = "*"
pylatexenc = "*"
python-docx = "*"
bashlex = "^0.18"

# TODO: These are integrations that should probably be optional
redis = ">=5.2,<7.0"
minio = "^7.2.8"
daytona = "0.21.1"
stripe = ">=11.5,<13.0"
google-cloud-aiplatform = "*"
anthropic = { extras = [ "vertex" ], version = "*" }
boto3 = "*"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
ruff = "0.11.13"
mypy = "1.16.0"
pre-commit = "4.2.0"
build = "*"
types-setuptools = "*"
pytest = "^8.4.0"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "*"
pytest-cov = "*"
pytest-asyncio = "*"
pytest-forked = "*"
pytest-xdist = "*"
openai = "*"
pandas = "*"
reportlab = "*"
gevent = ">=24.2.1,<26.0.0"

[tool.poetry.group.runtime]
optional = true

[tool.poetry.group.runtime.dependencies]
jupyterlab = "*"
notebook = "*"
jupyter_kernel_gateway = "*"
flake8 = "*"
memory-profiler = "^0.61.0"

[tool.poetry.group.evaluation]
optional = true

[tool.poetry.group.evaluation.dependencies]
streamlit = "*"
whatthepatch = "*"
retry = "*"
evaluate = "*"
swebench = "^3.0.8"
visualswebench = { git = "https://github.com/luolin101/Visual-SWE-bench.git" }
swegym = { git = "https://github.com/SWE-Gym/SWE-Bench-Package.git" }
commit0 = "*"
func_timeout = "*"
sympy = "*"
gdown = "*"
matplotlib = "*"
seaborn = "*"
tabulate = "*"
browsergym = "0.13.3"
browsergym-webarena = "0.13.3"
browsergym-miniwob = "0.13.3"
browsergym-visualwebarena = "0.13.3"
boto3-stubs = { extras = [ "s3" ], version = "^1.37.19" }
pyarrow = "20.0.0"                                                             # transitive dependency, pinned here to avoid conflicts
datasets = "*"
joblib = "*"

[tool.poetry.scripts]
openhands = "openhands.cli.main:main"

[tool.poetry.group.testgeneval.dependencies]
fuzzywuzzy = "^0.18.0"
rouge = "^1.0.1"
python-levenshtein = ">=0.26.1,<0.28.0"
tree-sitter-python = "^0.23.6"

[tool.poetry-dynamic-versioning]
enable = true
style = "semver"

[tool.autopep8]
# autopep8 fights with mypy on line length issue
ignore = [ "E501" ]

[tool.black]
# prevent black (if installed) from changing single quotes to double quotes
skip-string-normalization = true

[tool.ruff]
lint.select = [ "D" ]
# ignore warnings for missing docstrings
lint.ignore = [ "D1" ]
lint.pydocstyle.convention = "google"

[tool.coverage.run]
concurrency = [ "gevent" ]
