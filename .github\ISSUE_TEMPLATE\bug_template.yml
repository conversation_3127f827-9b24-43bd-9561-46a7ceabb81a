name: Bug
description: Report a problem with OpenHands
title: '[Bug]: '
labels: ['bug']
body:
  - type: markdown
    attributes:
      value: Thank you for taking the time to fill out this bug report. Please provide as much information as possible
       to help us understand and address the issue effectively.

  - type: checkboxes
    attributes:
      label: Is there an existing issue for the same bug? (If one exists, thumbs up or comment on the issue instead).
      description: Please check if an issue already exists for the bug you encountered.
      options:
      - label: I have checked the existing issues.
        required: true

  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug and reproduction steps
      description: Provide a description of the issue along with any reproduction steps.
    validations:
      required: true

  - type: dropdown
    id: installation
    attributes:
      label: OpenHands Installation
      description: How are you running OpenHands?
      options:
        - Docker command in README
        - GitHub resolver
        - Development workflow
        - CLI
        - app.all-hands.dev
        - Other
      default: 0

  - type: input
    id: openhands-version
    attributes:
      label: OpenHands Version
      description: What version of OpenHands are you using?
      placeholder: ex. 0.9.8, main, etc.

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      options:
        - MacOS
        - Linux
        - WSL on Windows

  - type: textarea
    id: additional-context
    attributes:
      label: Logs, Errors, Screenshots, and Additional Context
      description: Please provide any additional information you think might help. If you want to share the chat history
        you can click the thumbs-down (👎) button above the input field and you will get a shareable link
        (you can also click thumbs up when things are going well of course!). LLM logs will be stored in the
        `logs/llm/default` folder. Please add any additional context about the problem here.
