---
title: Search Engine Setup
description: Configure OpenHands to use <PERSON>ly as a search engine
---

## Setting Up Search Engine in OpenHands

OpenHands can be configured to use [<PERSON>ly](https://tavily.com/) as a search engine, which allows the agent to search the web for information when needed. This capability enhances the agent's ability to provide up-to-date information and solve problems that require external knowledge.

### Getting a Tavily API Key

To use the search functionality in OpenHands, you'll need to obtain a Tavily API key:

1. Visit [<PERSON><PERSON>'s website](https://tavily.com/) and sign up for an account
2. Navigate to the API section in your dashboard
3. Generate a new API key
4. Copy the API key (it should start with `tvly-`)

### Configuring Search in OpenHands

Once you have your Tavily API key, you can configure OpenHands to use it:

#### In the OpenHands UI

1. Open OpenHands and navigate to the Settings page by clicking the gear icon
2. In the LLM settings tab, locate the "Search API Key (Tavily)" field
3. Enter your Tavily API key (starting with `tvly-`)
4. Click "Save" to apply the changes

<Note>
The search API key field is optional. If you don't provide a key, the search functionality will not be available to the agent.
</Note>

#### Using Configuration Files

If you're running OpenHands in headless mode or via CLI, you can configure the search API key in your configuration file:

```toml
# In your OpenHands config file
[core]
search_api_key = "tvly-your-api-key-here"
```

### How Search Works in OpenHands

When the search engine is configured:

1. The agent can decide to search the web when it needs external information
2. Search queries are sent to Tavily's API via [Tavily's MCP server](https://github.com/tavily-ai/tavily-mcp) which includes a variety of [tools](https://docs.tavily.com/documentation/api-reference/introduction) (search, extract, crawl, map).
3. Results are returned and incorporated into the agent's context
4. The agent can use this information to provide more accurate and up-to-date responses

### Limitations

- Search results depend on Tavily's coverage and freshness
- Usage may be subject to Tavily's rate limits and pricing tiers
- The agent will only search when it determines that external information is needed

### Troubleshooting

If you encounter issues with the search functionality:

- Verify that your API key is correct and active
- Check that your API key starts with `tvly-`
- Ensure you have an active internet connection
- Check Tavily's status page for any service disruptions
