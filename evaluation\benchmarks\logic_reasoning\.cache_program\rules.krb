fact1
	foreach
		facts.Quiet($x, True)
		facts.Cold($x, True)
	assert
		facts.Smart($x, True)

fact2
	foreach
		facts.Red($x, True)
		facts.Cold($x, True)
	assert
		facts.Round($x, True)

fact3
	foreach
		facts.Kind($x, True)
		facts.Rough($x, True)
	assert
		facts.Red($x, True)

fact4
	foreach
		facts.Quiet($x, True)
	assert
		facts.Rough($x, True)

fact5
	foreach
		facts.Cold($x, True)
		facts.Smart($x, True)
	assert
		facts.Red($x, True)

fact6
	foreach
		facts.Rough($x, True)
	assert
		facts.Cold($x, True)

fact7
	foreach
		facts.Red($x, True)
	assert
		facts.Rough($x, True)

fact8
	foreach
		facts.<PERSON>(<PERSON>, True)
		facts.Kind(<PERSON>, <PERSON>)
	assert
		facts.Quiet(<PERSON>, <PERSON>)
