# Workflow that runs lint on the frontend and python code
name: Lint

# The jobs in this workflow are required, so they must run at all times
# Always run on "main"
# Always run on PRs
on:
  push:
    branches:
    - main
  pull_request:

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  # Run lint on the frontend code
  lint-frontend:
    name: Lint frontend
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - name: Install Node.js 20
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 20
      - name: Install dependencies
        run: |
          cd frontend
          npm install --frozen-lockfile
      - name: Lint, TypeScript compilation, and translation checks
        run: |
          cd frontend
          npm run lint
          npm run make-i18n && tsc
          npm run check-translation-completeness

  # Run lint on the python code
  lint-python:
    name: Lint python
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up python
        uses: useblacksmith/setup-python@v6
        with:
          python-version: 3.12
          cache: 'pip'
      - name: Install pre-commit
        run: pip install pre-commit==3.7.0
      - name: Run pre-commit hooks
        run: pre-commit run --all-files --show-diff-on-failure --config ./dev_config/python/.pre-commit-config.yaml

  # Check version consistency across documentation
  check-version-consistency:
    name: Check version consistency
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - name: Set up python
        uses: useblacksmith/setup-python@v6
        with:
          python-version: 3.12
      - name: Run version consistency check
        run: .github/scripts/check_version_consistency.py
